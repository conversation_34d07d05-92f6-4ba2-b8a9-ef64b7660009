<?php
/**
 * Interview Interviewees
 * List and manage interviewees for a specific position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews') ?>">Interview Management</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/sessions/1') ?>">Sessions</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('interviews/session/1/positions') ?>">Positions</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Interviewees - <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interviewees</h4>
                    <p class="text-muted mb-0">
                        <i class="fas fa-briefcase me-1"></i>
                        <?= esc($position['designation']) ?> (<?= esc($position['position_reference']) ?>)
                    </p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/session/1/positions') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Positions
                    </a>
                    <div class="btn-group">
                        <a href="<?= base_url('interviews/position/' . $position['id'] . '/questions') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-question-circle me-2"></i>Questions
                        </a>
                        <a href="<?= base_url('interviews/position/' . $position['id'] . '/scoring') ?>" class="btn btn-primary">
                            <i class="fas fa-star me-2"></i>Scoring
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Info Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="text-center">
                        <i class="fas fa-briefcase text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Position</h6>
                        <small class="text-muted"><?= esc($position['designation']) ?></small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <i class="fas fa-map-marker-alt text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Location</h6>
                        <small class="text-muted"><?= esc($position['location']) ?></small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <i class="fas fa-users text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Interviewees</h6>
                        <span class="fw-bold"><?= count($interviewees) ?></span>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <i class="fas fa-calendar-check text-info mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Scheduled</h6>
                        <span class="fw-bold"><?= count(array_filter($interviewees, function($i) { return $i['status'] === 'scheduled'; })) ?></span>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Completed</h6>
                        <span class="fw-bold"><?= count(array_filter($interviewees, function($i) { return $i['status'] === 'completed'; })) ?></span>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="text-center">
                        <i class="fas fa-star text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6 class="mb-0">Scored</h6>
                        <span class="fw-bold"><?= count(array_filter($interviewees, function($i) { return !is_null($i['score']); })) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interviewees List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        Interview Candidates
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm active" data-filter="all">
                            All
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="scheduled">
                            Scheduled
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="completed">
                            Completed
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" data-filter="scored">
                            Scored
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($interviewees)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Interviewees</h5>
                    <p class="text-muted">No candidates have been scheduled for interviews for this position yet.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Interviewees are automatically added when the interview schedule is generated.
                    </div>
                </div>
            <?php else: ?>
                <div class="row" id="intervieweesContainer">
                    <?php foreach ($interviewees as $interviewee): ?>
                        <div class="col-md-6 col-lg-4 mb-4 interviewee-item" 
                             data-status="<?= $interviewee['status'] ?>" 
                             data-scored="<?= !is_null($interviewee['score']) ? 'true' : 'false' ?>">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light border-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><?= esc($interviewee['applicant_name']) ?></h6>
                                        <?php if ($interviewee['status'] === 'scheduled'): ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-calendar-check me-1"></i>Scheduled
                                            </span>
                                        <?php elseif ($interviewee['status'] === 'completed'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>Completed
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <?= ucfirst($interviewee['status']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-envelope me-1"></i>
                                            <?= esc($interviewee['email']) ?>
                                        </small>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-phone me-1"></i>
                                            <?= esc($interviewee['phone']) ?>
                                        </small>
                                    </div>
                                    
                                    <div class="row g-2 mb-3">
                                        <div class="col-6">
                                            <div class="text-center p-2 bg-light rounded">
                                                <i class="fas fa-calendar text-primary"></i>
                                                <div class="small fw-bold"><?= date('M d', strtotime($interviewee['interview_date'])) ?></div>
                                                <div class="small text-muted">Date</div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center p-2 bg-light rounded">
                                                <i class="fas fa-clock text-success"></i>
                                                <div class="small fw-bold"><?= $interviewee['interview_time'] ?></div>
                                                <div class="small text-muted">Time</div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!is_null($interviewee['score'])): ?>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted">Score</small>
                                                <span class="fw-bold"><?= $interviewee['score'] ?>/100</span>
                                            </div>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar 
                                                    <?= $interviewee['score'] >= 80 ? 'bg-success' : 
                                                        ($interviewee['score'] >= 60 ? 'bg-warning' : 'bg-danger') ?>" 
                                                     style="width: <?= $interviewee['score'] ?>%"></div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="mb-3">
                                            <div class="text-center p-2 bg-light rounded">
                                                <i class="fas fa-star text-muted"></i>
                                                <div class="small text-muted">Not scored yet</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($interviewee['remarks']): ?>
                                        <div class="mb-3">
                                            <small class="text-muted d-block">Remarks:</small>
                                            <small class="text-truncate d-block" title="<?= esc($interviewee['remarks']) ?>">
                                                <?= esc($interviewee['remarks']) ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer bg-white border-0">
                                    <div class="btn-group w-100" role="group">
                                        <button type="button" 
                                                class="btn btn-outline-info btn-sm view-details-btn" 
                                                data-interviewee='<?= json_encode($interviewee) ?>'
                                                data-bs-toggle="modal" 
                                                data-bs-target="#detailsModal">
                                            <i class="fas fa-eye me-1"></i>Details
                                        </button>
                                        <button type="button" 
                                                class="btn btn-outline-primary btn-sm send-notification-btn" 
                                                data-candidate-email="<?= esc($interviewee['email']) ?>"
                                                data-candidate-name="<?= esc($interviewee['applicant_name']) ?>">
                                            <i class="fas fa-envelope me-1"></i>Notify
                                        </button>
                                        <a href="<?= base_url('interviews/position/' . $position['id'] . '/scoring') ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-star me-1"></i>Score
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <strong>Total Candidates:</strong> <?= count($interviewees) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Scheduled:</strong> <?= count(array_filter($interviewees, function($i) { return $i['status'] === 'scheduled'; })) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Completed:</strong> <?= count(array_filter($interviewees, function($i) { return $i['status'] === 'completed'; })) ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>Scored:</strong> <?= count(array_filter($interviewees, function($i) { return !is_null($i['score']); })) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="fas fa-user me-2"></i>Candidate Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Candidate Name</label>
                        <p class="form-control-plaintext" id="details_candidate_name">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Position</label>
                        <p class="form-control-plaintext"><?= esc($position['designation']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Email Address</label>
                        <p class="form-control-plaintext" id="details_email">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Phone Number</label>
                        <p class="form-control-plaintext" id="details_phone">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview Date</label>
                        <p class="form-control-plaintext" id="details_date">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview Time</label>
                        <p class="form-control-plaintext" id="details_time">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Status</label>
                        <p class="form-control-plaintext" id="details_status">-</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview Score</label>
                        <p class="form-control-plaintext" id="details_score">-</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Remarks</label>
                        <p class="form-control-plaintext" id="details_remarks">-</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="<?= base_url('interviews/position/' . $position['id'] . '/scoring') ?>" class="btn btn-primary">
                    <i class="fas fa-star me-2"></i>Go to Scoring
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter interviewee items
            const items = document.querySelectorAll('.interviewee-item');
            items.forEach(item => {
                let show = false;
                
                if (filter === 'all') {
                    show = true;
                } else if (filter === 'scheduled') {
                    show = item.getAttribute('data-status') === 'scheduled';
                } else if (filter === 'completed') {
                    show = item.getAttribute('data-status') === 'completed';
                } else if (filter === 'scored') {
                    show = item.getAttribute('data-scored') === 'true';
                }
                
                item.style.display = show ? '' : 'none';
            });
        });
    });

    // Details modal
    const detailsModal = document.getElementById('detailsModal');
    if (detailsModal) {
        detailsModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const interviewee = JSON.parse(button.getAttribute('data-interviewee'));
            
            document.getElementById('details_candidate_name').textContent = interviewee.applicant_name;
            document.getElementById('details_email').textContent = interviewee.email;
            document.getElementById('details_phone').textContent = interviewee.phone;
            document.getElementById('details_date').textContent = new Date(interviewee.interview_date).toLocaleDateString();
            document.getElementById('details_time').textContent = interviewee.interview_time;
            document.getElementById('details_status').textContent = interviewee.status.charAt(0).toUpperCase() + interviewee.status.slice(1);
            document.getElementById('details_score').textContent = interviewee.score ? `${interviewee.score}/100` : 'Not scored yet';
            document.getElementById('details_remarks').textContent = interviewee.remarks || 'No remarks available';
        });
    }

    // Send notification
    document.querySelectorAll('.send-notification-btn').forEach(button => {
        button.addEventListener('click', function() {
            const email = this.getAttribute('data-candidate-email');
            const name = this.getAttribute('data-candidate-name');
            
            if (confirm(`Send interview notification to ${name} (${email})?`)) {
                // Mock notification sending
                if (typeof toastr !== 'undefined') {
                    toastr.success(`Notification sent to ${name}`);
                } else {
                    alert(`Notification sent to ${name}`);
                }
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
